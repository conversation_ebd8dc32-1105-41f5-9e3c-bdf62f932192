package config

import (
	"bytes"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/joho/godotenv"
	"github.com/spf13/viper"
)

type DownstreamServicesConfig struct {
	AuthServiceAddr    string `mapstructure:"auth_service_addr"`
	UserServiceAddr    string `mapstructure:"user_service_addr"`
	ProductServiceAddr string `mapstructure:"product_service_addr"`
	VoucherServiceAddr string `mapstructure:"voucher_service_addr"`
	OrderServiceAddr   string `mapstructure:"order_service_addr"`
}

type Config struct {
	Service            ServiceConfig            `mapstructure:"service"`
	Database           DatabaseConfig           `mapstructure:"database"`
	Redis              RedisConfig              `mapstructure:"redis"`
	GRPC               GRPCConfig               `mapstructure:"grpc"`
	Kafka              KafkaConfig              `mapstructure:"kafka"`
	Jaeger             JaegerConfig             `mapstructure:"jaeger"`
	Auth               AuthConfig               `mapstructure:"auth"`
	Logging            LoggingConfig            `mapstructure:"logging"`
	Metrics            MetricsConfig            `mapstructure:"metrics"`
	DownstreamServices DownstreamServicesConfig `mapstructure:"downstream_services"`
}

type ServiceConfig struct {
	Name        string `mapstructure:"name"`
	Version     string `mapstructure:"version"`
	Environment string `mapstructure:"environment"`
	Port        int    `mapstructure:"port"`
	GRPCPort    int    `mapstructure:"grpc_port"`
	ClientID    string `mapstructure:"client_id"`
	ClientKey   string `mapstructure:"client_key"`
}

type DatabaseConfig struct {
	Host         string        `mapstructure:"host"`
	Port         int           `mapstructure:"port"`
	User         string        `mapstructure:"user"`
	Password     string        `mapstructure:"password"`
	Name         string        `mapstructure:"name"`
	SSLMode      string        `mapstructure:"ssl_mode"`
	MaxOpenConns int           `mapstructure:"max_open_conns"`
	MaxIdleConns int           `mapstructure:"max_idle_conns"`
	MaxLifetime  time.Duration `mapstructure:"max_lifetime"`
}

type RedisConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
}

type GRPCConfig struct {
	Host              string        `mapstructure:"host"`
	Port              int           `mapstructure:"port"`
	MaxReceiveSize    int           `mapstructure:"max_receive_size"`
	MaxSendSize       int           `mapstructure:"max_send_size"`
	ConnectionTimeout time.Duration `mapstructure:"connection_timeout"`
	KeepaliveTime     time.Duration `mapstructure:"keepalive_time"`
	KeepaliveTimeout  time.Duration `mapstructure:"keepalive_timeout"`
	MaxConnectionIdle time.Duration `mapstructure:"max_connection_idle"`
}

type KafkaConfig struct {
	Brokers []string          `mapstructure:"brokers"`
	GroupID string            `mapstructure:"group_id"`
	Topics  KafkaTopicsConfig `mapstructure:"topics"`
}

type KafkaTopicsConfig struct{}

type JaegerConfig struct {
	Host string `mapstructure:"host"`
	Port int    `mapstructure:"port"`
}

type AuthConfig struct {
	JWTSecret              string        `mapstructure:"jwt_secret"`
	JWTExpiration          time.Duration `mapstructure:"jwt_expiration"`
	RefreshTokenExpiration time.Duration `mapstructure:"refresh_token_expiration"`
	BootstrapToken         string        `mapstructure:"bootstrap_token"`
}

type LoggingConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
}

type MetricsConfig struct {
	Port int    `mapstructure:"port"`
	Path string `mapstructure:"path"`
}

func Load() (*Config, error) {
	if err := loadEnvFile(); err != nil {
		fmt.Printf("Warning: Could not load .env file: %v\n", err)
	}

	v := viper.New()
	v.SetConfigName("config")
	v.SetConfigType("yaml")
	v.AddConfigPath(".")
	v.AddConfigPath("./config")
	v.AddConfigPath("/etc/coupon-services/config")

	v.AutomaticEnv()
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	setDefaults(v)

	if err := v.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	if err := expandEnvConfig(v); err != nil {
		return nil, err
	}

	var config Config
	if err := v.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

func loadEnvFile() error {
	envPaths := []string{
		".env",
		"./config/.env",
		"./.env",
	}

	var lastErr error
	for _, path := range envPaths {
		if err := godotenv.Load(path); err == nil {
			fmt.Printf("Loaded environment file: %s\n", path)
			return nil
		} else {
			lastErr = err
		}
	}

	return lastErr
}

func setDefaults(v *viper.Viper) {
	v.SetDefault("service.environment", "development")
	v.SetDefault("service.port", 8080)
	v.SetDefault("service.grpc_port", 9090)
	v.SetDefault("database.host", "localhost")
	v.SetDefault("database.port", 5432)
	v.SetDefault("database.ssl_mode", "disable")
	v.SetDefault("database.max_open_conns", 25)
	v.SetDefault("database.max_idle_conns", 25)
	v.SetDefault("database.max_lifetime", time.Hour)
	v.SetDefault("redis.host", "localhost")
	v.SetDefault("redis.port", 6379)
	v.SetDefault("redis.db", 0)
	v.SetDefault("grpc.host", "localhost")
	v.SetDefault("grpc.max_receive_size", 4*1024*1024)
	v.SetDefault("grpc.max_send_size", 4*1024*1024)
	v.SetDefault("grpc.connection_timeout", 5*time.Second)
	v.SetDefault("grpc.keepalive_time", 30*time.Second)
	v.SetDefault("grpc.keepalive_timeout", 5*time.Second)
	v.SetDefault("grpc.max_connection_idle", 90*time.Second)
	v.SetDefault("kafka.brokers", []string{"localhost:9092"})
	v.SetDefault("jaeger.host", "localhost")
	v.SetDefault("jaeger.port", 14268)
	v.SetDefault("auth.jwt_expiration", 1*time.Hour)
	v.SetDefault("auth.refresh_token_expiration", 720*time.Hour)
	v.SetDefault("logging.level", "info")
	v.SetDefault("logging.format", "json")
	v.SetDefault("metrics.port", 2112)
	v.SetDefault("metrics.path", "/metrics")
}

func expandEnvConfig(v *viper.Viper) error {
	configFile := v.ConfigFileUsed()
	if configFile == "" {
		return nil
	}
	data, err := os.ReadFile(configFile)
	if err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	expanded := os.ExpandEnv(string(data))

	if err := v.ReadConfig(bytes.NewBuffer([]byte(expanded))); err != nil {
		return fmt.Errorf("failed to parse expanded config: %w", err)
	}
	return nil
}
